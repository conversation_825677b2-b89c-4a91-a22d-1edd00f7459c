<!-- 发布笔记模态框 -->
<div class="modal" id="postModal">
    <div class="modal-content">
        <div class="modal-header">
            <h5>发布笔记</h5>
            <button type="button" class="close-button" data-dismiss="modal">&times;</button>
        </div>
        <div class="modal-body">
            <form id="postForm" class="post-form">
                <div class="form-group">
                    <!-- Markdown编辑器工具栏 -->
                    <div class="markdown-toolbar">
                        <button type="button" class="toolbar-btn" data-action="bold" title="粗体">
                            <strong>B</strong>
                        </button>
                        <button type="button" class="toolbar-btn" data-action="italic" title="斜体">
                            <em>I</em>
                        </button>
                        <button type="button" class="toolbar-btn" data-action="heading" title="标题">
                            H
                        </button>
                        <button type="button" class="toolbar-btn" data-action="link" title="链接">
                            🔗
                        </button>
                        <button type="button" class="toolbar-btn" data-action="code" title="代码">
                            &lt;/&gt;
                        </button>
                        <button type="button" class="toolbar-btn" data-action="list" title="列表">
                            ≡
                        </button>
                        <div class="toolbar-divider"></div>
                        <button type="button" class="toolbar-btn preview-btn" data-action="preview" title="预览">
                            👁
                        </button>
                    </div>

                    <!-- 编辑器容器 -->
                    <div class="editor-container">
                        <textarea name="content" id="markdownEditor" placeholder="支持Markdown格式，有什么想法想记录？" required></textarea>
                        <div class="preview-container" id="previewContainer" style="display: none;">
                            <div class="preview-content" id="previewContent"></div>
                        </div>
                    </div>
                </div>
                    <div class="form-footer">
                        <div class="tag-section">
                            <div class="tags-input-wrapper">
                                <div class="tags-container" id="tagsContainer"></div>
                                <input type="text" id="tagInput" placeholder="添加标签" />
                                <!-- 发布按钮 -->
                                <div class="flex-shrink-0 ml-2">
                                    <button type="submit" form="postForm" class="btn-primary">发布</button>
                                </div>
                            </div>
                            <input type="hidden" name="tags" id="tagsField" value="[]" />
                        </div>
                    </div>
            </form>
        </div>
    </div>
</div>

<!-- 发布笔记按钮 -->
<button id="postButton" class="post-button">发布笔记</button>

<style>
/* 发布笔记模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    overflow: auto;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: white;
    margin: auto;
    padding: 0;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    font-weight: bold;
    color: #718096;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-button:hover {
    background-color: #f7fafc;
    color: #2d3748;
}

.modal-body {
    padding: 1.5rem 2rem;
}

/* Markdown工具栏样式 */
.markdown-toolbar {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
    border-radius: 12px 12px 0 0;
    flex-wrap: wrap;
}

.toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    transition: all 0.2s ease;
}

.toolbar-btn:hover {
    background: #e2e8f0;
    color: #475569;
}

.toolbar-btn.active {
    background: #6366f1;
    color: white;
}

.toolbar-divider {
    width: 1px;
    height: 24px;
    background: #e2e8f0;
    margin: 0 4px;
}

.preview-btn.active {
    background: #10b981;
    color: white;
}

/* 编辑器容器样式 */
.editor-container {
    position: relative;
    border: 2px solid #e2e8f0;
    border-top: none;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

.modal-body textarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: none;
    font-size: 16px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    resize: vertical;
    outline: none;
    transition: border-color 0.3s ease;
    background: white;
    border-radius: 0;
}

.modal-body textarea:focus {
    border-color: transparent;
    box-shadow: none;
}

/* 预览容器样式 */
.preview-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    overflow-y: auto;
}

.preview-content {
    padding: 1rem;
    line-height: 1.6;
    color: #374151;
}

/* Markdown渲染样式 */
.preview-content h1, .preview-content h2, .preview-content h3,
.preview-content h4, .preview-content h5, .preview-content h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    color: #1f2937;
}

.preview-content h1 { font-size: 1.5em; }
.preview-content h2 { font-size: 1.3em; }
.preview-content h3 { font-size: 1.1em; }

.preview-content p {
    margin: 8px 0;
}

.preview-content strong {
    font-weight: 600;
    color: #1f2937;
}

.preview-content em {
    font-style: italic;
}

.preview-content code {
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #e11d48;
}

.preview-content pre {
    background: #f8fafc;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 12px 0;
    border: 1px solid #e2e8f0;
}

.preview-content pre code {
    background: none;
    padding: 0;
    color: #374151;
}

.preview-content ul, .preview-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

.preview-content li {
    margin: 4px 0;
}

.preview-content a {
    color: #6366f1;
    text-decoration: underline;
}

.preview-content a:hover {
    color: #4f46e5;
}

.preview-content blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 16px;
    margin: 12px 0;
    color: #6b7280;
    font-style: italic;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 1rem 2rem 1.5rem;
    gap: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 1rem;
}

.form-footer {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
}

.tag-section {
    flex: 1;
}

.tags-input-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    transition: border-color 0.3s ease;
}

.tags-input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.tags-input-wrapper input[type="text"] {
    border: none;
    outline: none;
    flex: 1;
    min-width: 120px;
    font-size: 14px;
    background: transparent;
    color: #2d3748;
}

.tags-input-wrapper .tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.tags-input-wrapper .tag .remove-tag {
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.tags-input-wrapper .tag .remove-tag:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.post-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.post-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .form-footer {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tags-input-wrapper {
        flex-direction: column;
        align-items: stretch;
    }
}
</style>

<script>
// 标签管理器类
class TagManager {
    constructor(options) {
        this.tagInput = options.tagInput;
        this.tagsContainer = options.tagsContainer;
        this.tagsField = options.tagsField;
        this.tags = options.initialTags || [];
        
        this.init();
    }
    
    init() {
        this.tagInput.addEventListener('keydown', this.handleKeydown.bind(this));
        this.tagsContainer.addEventListener('click', this.handleTagClick.bind(this));
        this.updateTags();
    }
    
    handleKeydown(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const tag = this.tagInput.value.trim();
            if (tag && !this.tags.includes(tag)) {
                this.addTag(tag);
                this.tagInput.value = '';
            }
        }
    }
    
    handleTagClick(e) {
        if (e.target.classList.contains('remove-tag')) {
            const index = parseInt(e.target.getAttribute('data-index'));
            this.removeTag(index);
        }
    }
    
    addTag(tag) {
        if (tag && !this.tags.includes(tag)) {
            this.tags.push(tag);
            this.updateTags();
        }
    }
    
    removeTag(index) {
        this.tags.splice(index, 1);
        this.updateTags();
    }
    
    updateTags() {
        this.tagsContainer.innerHTML = '';
        this.tags.forEach((tag, index) => {
            const tagElement = document.createElement('span');
            tagElement.className = 'tag';
            tagElement.innerHTML = `${tag}<span class="remove-tag" data-index="${index}">&times;</span>`;
            this.tagsContainer.appendChild(tagElement);
        });
        this.tagsField.value = JSON.stringify(this.tags);
    }
    
    getTags() {
        return [...this.tags];
    }
    
    setTags(tags) {
        this.tags = Array.isArray(tags) ? [...tags] : [];
        this.updateTags();
    }
    
    clearTags() {
        this.tags = [];
        this.updateTags();
    }
    
    processRemainingTag() {
        const tag = this.tagInput.value.trim();
        if (tag && !this.tags.includes(tag)) {
            this.addTag(tag);
            this.tagInput.value = '';
            return true;
        }
        return false;
    }
}

// Markdown编辑器控制器
class MarkdownEditor {
    constructor(textareaId, previewContainerId, previewContentId) {
        this.textarea = document.getElementById(textareaId);
        this.previewContainer = document.getElementById(previewContainerId);
        this.previewContent = document.getElementById(previewContentId);
        this.isPreviewMode = false;

        this.init();
    }

    init() {
        // 绑定工具栏按钮事件
        document.querySelectorAll('.toolbar-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.handleToolbarClick(e));
        });
    }

    handleToolbarClick(e) {
        e.preventDefault();
        const action = e.target.closest('.toolbar-btn').getAttribute('data-action');

        switch(action) {
            case 'bold':
                this.insertMarkdown('**', '**', '粗体文本');
                break;
            case 'italic':
                this.insertMarkdown('*', '*', '斜体文本');
                break;
            case 'heading':
                this.insertMarkdown('## ', '', '标题');
                break;
            case 'link':
                this.insertMarkdown('[', '](http://)', '链接文本');
                break;
            case 'code':
                this.insertMarkdown('`', '`', '代码');
                break;
            case 'list':
                this.insertMarkdown('- ', '', '列表项');
                break;
            case 'preview':
                this.togglePreview(e.target.closest('.toolbar-btn'));
                break;
        }
    }

    insertMarkdown(before, after, placeholder) {
        const start = this.textarea.selectionStart;
        const end = this.textarea.selectionEnd;
        const selectedText = this.textarea.value.substring(start, end);
        const text = selectedText || placeholder;

        const newText = before + text + after;
        this.textarea.value = this.textarea.value.substring(0, start) + newText + this.textarea.value.substring(end);

        // 设置光标位置
        const newStart = start + before.length;
        const newEnd = newStart + text.length;
        this.textarea.focus();
        this.textarea.setSelectionRange(newStart, newEnd);
    }

    async togglePreview(button) {
        this.isPreviewMode = !this.isPreviewMode;

        if (this.isPreviewMode) {
            // 显示预览
            button.classList.add('active');
            await this.renderPreview();
            this.previewContainer.style.display = 'block';
        } else {
            // 隐藏预览
            button.classList.remove('active');
            this.previewContainer.style.display = 'none';
        }
    }

    async renderPreview() {
        const content = this.textarea.value;
        try {
            // 发送到后端渲染Markdown
            const response = await fetch('/api/markdown/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({ content: content })
            });

            if (response.ok) {
                const data = await response.json();
                this.previewContent.innerHTML = data.rendered_content;
            } else {
                // 如果API不可用，使用简单的客户端渲染
                this.previewContent.innerHTML = this.simpleMarkdownRender(content);
            }
        } catch (error) {
            // 使用简单的客户端渲染作为后备
            this.previewContent.innerHTML = this.simpleMarkdownRender(content);
        }
    }

    simpleMarkdownRender(content) {
        // 简单的客户端Markdown渲染（后备方案）
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/^\- (.*$)/gim, '<li>$1</li>')
            .replace(/\n/g, '<br>');
    }

    getValue() {
        return this.textarea.value;
    }

    setValue(value) {
        this.textarea.value = value;
    }

    clear() {
        this.textarea.value = '';
        this.previewContent.innerHTML = '';
    }
}

// 发布笔记模态框控制器
class PostModalController {
    constructor(options = {}) {
        this.createPostUrl = options.createPostUrl || '/post/create';
        this.onPostCreated = options.onPostCreated || this.defaultOnPostCreated.bind(this);

        this.init();
    }
    
    init() {
        this.postButton = document.getElementById('postButton');
        this.postModal = document.getElementById('postModal');
        this.postForm = document.getElementById('postForm');

        // 初始化Markdown编辑器
        this.markdownEditor = new MarkdownEditor('markdownEditor', 'previewContainer', 'previewContent');

        // 初始化标签管理器
        this.tagManager = new TagManager({
            tagInput: document.getElementById('tagInput'),
            tagsContainer: document.getElementById('tagsContainer'),
            tagsField: document.getElementById('tagsField')
        });

        this.bindEvents();
    }
    
    bindEvents() {
        // 发布按钮点击事件
        this.postButton.addEventListener('click', () => this.showModal());
        
        // 关闭模态框事件
        this.postModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
            button.addEventListener('click', () => this.hideModal());
        });
        
        // 点击背景关闭（仅通过关闭按钮）
        this.postModal.addEventListener('click', (event) => {
            if (event.target.classList.contains('close-button')) {
                this.hideModal();
            }
        });
        
        // 表单提交事件
        this.postForm.addEventListener('submit', (e) => this.handleSubmit(e));
    }
    
    showModal() {
        this.postModal.classList.add('show');
    }
    
    hideModal() {
        this.postModal.classList.remove('show');
        this.postForm.reset();
        this.tagManager.clearTags();
    }
    
    handleSubmit(e) {
        e.preventDefault();
        
        // 检查未添加的标签
        this.tagManager.processRemainingTag();
        
        const formData = new FormData(this.postForm);
        formData.append('tags', JSON.stringify(this.tagManager.getTags()));
        
        fetch(this.createPostUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(formData)
        })
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => {
                    try {
                        const errorData = JSON.parse(text);
                        throw new Error(errorData.message || '请求失败');
                    } catch {
                        throw new Error(`服务器错误 (${response.status}): ${text.slice(0, 100)}`);
                    }
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                this.onPostCreated(data.post);
            } else {
                alert('发布失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('发布失败，请重试');
        })
        .finally(() => {
            this.hideModal();
        });
    }
    
    defaultOnPostCreated(post) {  
        // 默认行为：刷新页面
        window.location.reload();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否存在必要的元素
    if (document.getElementById('postButton') && document.getElementById('postModal')) {
        // 创建发布笔记控制器
        window.postModalController = new PostModalController({
            createPostUrl: window.API_URLS?.createPost || '/post/create',
            onPostCreated: window.onPostCreated || undefined
        });
    }
});
</script>
